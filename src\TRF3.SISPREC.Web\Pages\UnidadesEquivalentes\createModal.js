$(function () {
    const unidadeOrcamentariaService = tRF3.sISPREC.unidadesOrcamentarias.unidadeOrcamentaria;
    const unidadeEquivalenteService = tRF3.sISPREC.unidadeEquivalentes.unidadeEquivalente;

    // Inicialização do Select2 para NomeUnidade e NomeUnidadeEquivalente
    $('#ViewModel_NomeUnidade, #ViewModel_NomeUnidadeEquivalente').select2({
        dropdownParent: $('#CreateModal'),
        theme: 'bootstrap-5',
        minimumInputLength: 3,
        ajax: {
            url: abp.appPath + 'api/app/unidade-orcamentaria/todos-os-nomes-unidades-orcamentarias-sem-data-utilizacao-fim',
            dataType: 'json',
            data: function (params) {
                let query = {
                    nome: params.term
                };
                return query;
            },
            processResults: function (data) {
                let result = $.map(data, function (obj) {
                    obj.id = obj.codigoSIAFI;
                    obj.text = obj.unidadeDto.nome;

                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    // Inicialização do Select2 para CodigoSiafi e CodigoSiafiEquivalente
    $('#ViewModel_CodigoSiafi, #ViewModel_CodigoSiafiEquivalente').select2({
        dropdownParent: $('#CreateModal'),
        theme: 'bootstrap-5',
        minimumInputLength: 3,
        ajax: {
            url: abp.appPath + 'api/app/unidade-orcamentaria/todos-os-codigo-siafi-unidades-orcamentarias',
            dataType: 'json',
            data: function (params) {
                let query = {
                    codigoSIAFI: params.term
                };
                return query;
            },
            processResults: function (data) {
                let result = $.map(data, function (obj) {
                    obj.id = obj.codigoSIAFI;
                    obj.text = obj.codigoSIAFI;

                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    // Ao selecionar um Código SIAFI, buscar o nome da unidade e preencher o campo NomeUnidade diretamente,
    // sem abrir o dropdown, evitando o problema do duplo clique.
    $('#ViewModel_CodigoSiafi').on('change', function () {
        let codigoSiafiSelecionado = $('#ViewModel_CodigoSiafi').val();
        unidadeOrcamentariaService.getNomeUnidadePeloCodigoSiafi(codigoSiafiSelecionado)
            .then(function (result) {
                // Limpa o valor atual de NomeUnidade
                $('#ViewModel_NomeUnidade').val(null).trigger('change');

                // Cria uma nova option com o nome retornado e código SIAFI como valor
                let newOption = new Option(result, codigoSiafiSelecionado, true, true);
                // Adiciona a nova option ao select
                $('#ViewModel_NomeUnidade').append(newOption).trigger('change');
            });
    });

    // Ao selecionar um NomeUnidade, atualizar o Código SIAFI correspondente diretamente
    $('#ViewModel_NomeUnidade').on('select2:select', function (e) {
        let data = e.params.data;
        // data.id é o código SIAFI

        let newOption = new Option(data.id, data.id, true, true);
        $('#ViewModel_CodigoSiafi').append(newOption).trigger('change');
    });

    // Ao selecionar um Código SIAFI equivalente, buscar o nome da unidade equivalente e preencher o campo NomeUnidadeEquivalente,
    // sem abrir o dropdown para evitar o problema.
    $('#ViewModel_CodigoSiafiEquivalente').on('change', function () {
        let codigoSiafiEquivalenteSelecionado = $('#ViewModel_CodigoSiafiEquivalente').val();
        unidadeOrcamentariaService.getNomeUnidadePeloCodigoSiafi(codigoSiafiEquivalenteSelecionado)
            .then(function (result) {
                $('#ViewModel_NomeUnidadeEquivalente').val(null).trigger('change');
                let newOption = new Option(result, codigoSiafiEquivalenteSelecionado, true, true);
                $('#ViewModel_NomeUnidadeEquivalente').append(newOption).trigger('change');
            });
    });

    // Ao selecionar um NomeUnidadeEquivalente, atualizar o Código SIAFI equivalente correspondente diretamente
    $('#ViewModel_NomeUnidadeEquivalente').on('select2:select', function (e) {
        let data = e.params.data;

        let newOption = new Option(data.id, data.id, true, true);
        $('#ViewModel_CodigoSiafiEquivalente').append(newOption).trigger('change');
    });

    $('#btnCreateUnidadeEquivalente').on('click', function (e) {
        e.preventDefault();
        //const $form = $('form#CreateUnidadeEquivalente').off('submit');
        const $form = $('form#CreateUnidadeEquivalente');

        const codigoSiafi = $('#ViewModel_CodigoSiafi').val();
        const codigoSiafiEquivalente = $('#ViewModel_CodigoSiafiEquivalente').val();

        // Verifica se a unidade equivalente já existe
        unidadeEquivalenteService.get({ codSiafi: codigoSiafi, codSiafiEquivalente: codigoSiafiEquivalente })
            .then(function (resultado) {
                if (!resultado) {
                    // Não existe - submete normalmente
                    $form.submit();
                }
                else if (resultado.ativo) {
                    // Existe e está ativa - apenas exibe mensagem
                    abp.message.warn(
                        'Esta equivalência já existe e está ativa.',
                        'Equivalência já cadastrada'
                    );
                }
                else {
                    // Existe mas está inativa - pergunta se quer reativar
                    alertaBotoesInvertidos('Reativar equivalência', 'Esta equivalência já existe, mas está inativa. Deseja reativá-la?', 'warning')
                        .then((result) => {
                            if (result.isConfirmed) {
                                unidadeEquivalenteService.ativarDesativar({ codSiafi: codigoSiafi, codSiafiEquivalente: codigoSiafiEquivalente })
                                    .then(function () {
                                        abp.notify.info('Equivalência reativada.');
                                    })
                                    .catch(function (error) {
                                        abp.message.error(
                                            'Erro ao reativar a equivalência.' + error
                                        );
                                    })
                                    .always(function () {
                                        abp.ui.unblock();
                                        $('#CreateModal').modal('hide');
                                    });
                            }
                        });
                }
            });
    });
});